.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex: 1;
  margin-left: 280px; // Default sidebar width
  padding: 2rem;
  overflow-y: auto;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  position: relative;

  &.sidebar-expanded {
    margin-left: 280px;
  }

  &:not(.sidebar-expanded) {
    margin-left: 80px; // Collapsed sidebar width
  }

  // Add some visual enhancement
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
    z-index: 10;
  }
}

// Mobile toggle button (when sidebar is collapsed)
.mobile-toggle {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1001;
  background-color: #1e293b;
  border: none;
  color: #fff;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  display: none;

  &:hover {
    background-color: #334155;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  .hamburger-icon {
    display: flex;
    flex-direction: column;
    width: 20px;
    height: 15px;
    justify-content: space-between;

    span {
      display: block;
      height: 2px;
      width: 100%;
      background-color: #fff;
      transition: all 0.3s ease;
    }
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .main-content {
    margin-left: 0 !important; // No margin on mobile
    padding: 1rem;
    padding-top: 5rem; // Account for mobile toggle button

    &::before {
      display: none; // Hide the top gradient line on mobile
    }
  }

  .mobile-toggle {
    display: block;
    backdrop-filter: blur(10px);
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}