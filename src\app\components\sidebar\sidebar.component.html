<nav class="sidebar" [class.collapsed]="isCollapsed" [class.mobile]="isMobile">
  <div class="sidebar-header">
    <div class="logo" [class.hidden]="isCollapsed && isMobile">
      <div class="logo-icon">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="logo-text">
        <h2>Social Media</h2>
        <span class="subtitle">Management Tool</span>
      </div>
    </div>
    <button class="toggle-btn" (click)="toggleSidebar()" [attr.aria-label]="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="1" fill="currentColor"/>
        <circle cx="19" cy="12" r="1" fill="currentColor"/>
        <circle cx="5" cy="12" r="1" fill="currentColor"/>
      </svg>
    </button>
  </div>

  <div class="nav-section" [class.hidden]="isCollapsed && isMobile">
    <div class="section-title" [class.hidden]="isCollapsed && !isMobile">
      <span>Platforms</span>
    </div>
    <ul class="nav-links">
      <li>
        <a [routerLink]="'/instagram'" routerLinkActive="active" class="nav-link">
          <div class="icon-wrapper instagram">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="currentColor" stroke-width="2"/>
              <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37Z" stroke="currentColor" stroke-width="2"/>
              <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <div class="link-content" [class.hidden]="isCollapsed && !isMobile">
            <span class="text">Instagram</span>
            <span class="description">Direct Messages</span>
          </div>
          <div class="notification-badge" [class.hidden]="isCollapsed && !isMobile">3</div>
        </a>
      </li>
      <li>
        <a [routerLink]="'/whatsapp'" routerLinkActive="active" class="nav-link">
          <div class="icon-wrapper whatsapp">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 11.5A8.38 8.38 0 0 1 12.62 20A8.38 8.38 0 0 1 3 11.5A8.38 8.38 0 0 1 12.62 3A8.38 8.38 0 0 1 21 11.5Z" stroke="currentColor" stroke-width="2"/>
              <path d="M8.21 13.89L7 23L16.11 15.79C17.18 14.72 18 13.26 18 11.5A6.38 6.38 0 0 0 12.62 5A6.38 6.38 0 0 0 7 11.5C7 12.81 7.35 14.02 8.21 13.89Z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="link-content" [class.hidden]="isCollapsed && !isMobile">
            <span class="text">WhatsApp</span>
            <span class="description">Business Chat</span>
          </div>
          <div class="notification-badge" [class.hidden]="isCollapsed && !isMobile">7</div>
        </a>
      </li>
    </ul>
  </div>

  <div class="sidebar-footer" [class.hidden]="isCollapsed && isMobile">
    <div class="user-profile" [class.hidden]="isCollapsed && !isMobile">
      <div class="avatar">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <div class="user-info">
        <span class="username">Admin User</span>
        <span class="status">Online</span>
      </div>
    </div>
  </div>
</nav>

<!-- Mobile overlay -->
<div class="sidebar-overlay"
     [class.active]="!isCollapsed && isMobile"
     (click)="toggleSidebar()">
</div>