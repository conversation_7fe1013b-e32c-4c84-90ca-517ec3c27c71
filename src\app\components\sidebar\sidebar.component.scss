.sidebar {
  width: 240px;
  height: 100vh;
  background-color: #1e293b;
  color: #fff;
  padding: 1rem;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    .logo {
      font-size: 1.5rem;
      font-weight: bold;
      transition: opacity 0.3s ease;

      h2 {
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
      }

      &.hidden {
        opacity: 0;
        pointer-events: none;
      }
    }

    .toggle-btn {
      background: none;
      border: none;
      color: #fff;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 4px;
      transition: background-color 0.3s ease;
      display: block; // Always visible

      &:hover {
        background-color: #334155;
      }

      .hamburger-icon {
        display: flex;
        flex-direction: column;
        width: 20px;
        height: 15px;
        justify-content: space-between;

        span {
          display: block;
          height: 2px;
          width: 100%;
          background-color: #fff;
          transition: all 0.3s ease;
        }
      }
    }
  }

  .nav-links {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;

    li {
      margin: 0.5rem 0;

      a {
        color: #cbd5e1;
        text-decoration: none;
        font-size: 1rem;
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        position: relative;

        .icon {
          font-size: 1.2rem;
          margin-right: 0.75rem;
          min-width: 24px;
          text-align: center;
        }

        .text {
          transition: opacity 0.3s ease;
          white-space: nowrap;
          overflow: hidden;

          &.hidden {
            opacity: 0;
            width: 0;
            margin: 0;
          }
        }

        &:hover {
          background-color: #334155;
          color: #fff;
          transform: translateX(4px);
        }

        &.active {
          background-color: #3b82f6;
          color: #fff;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: #60a5fa;
            border-radius: 0 2px 2px 0;
          }
        }
      }
    }

    &.hidden {
      display: none;
    }
  }

  // Collapsed state for desktop
  &.collapsed:not(.mobile) {
    width: 70px;

    .sidebar-header .logo {
      opacity: 0;
      pointer-events: none;
    }

    .nav-links a .text {
      opacity: 0;
      width: 0;
      margin: 0;
    }
  }

  // Mobile styles
  &.mobile {
    transform: translateX(-100%);
    width: 280px;

    .sidebar-header .toggle-btn {
      display: block;
    }

    &:not(.collapsed) {
      transform: translateX(0);
    }
  }
}

// Desktop toggle button (always visible)
@media (min-width: 769px) {
  .sidebar .sidebar-header .toggle-btn {
    display: block !important;
  }
}

// Sidebar overlay for mobile
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Mobile toggle button (when sidebar is collapsed)
@media (max-width: 768px) {
  .mobile-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background-color: #1e293b;
    border: none;
    color: #fff;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &:hover {
      background-color: #334155;
    }

    .hamburger-icon {
      display: flex;
      flex-direction: column;
      width: 20px;
      height: 15px;
      justify-content: space-between;

      span {
        display: block;
        height: 2px;
        width: 100%;
        background-color: #fff;
        transition: all 0.3s ease;
      }
    }
  }
}