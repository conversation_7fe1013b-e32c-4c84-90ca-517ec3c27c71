.sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  color: #fff;
  padding: 1.5rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.1);

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .logo {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      transition: all 0.3s ease;

      .logo-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 10px;
        color: white;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }

      .logo-text {
        display: flex;
        flex-direction: column;

        h2 {
          margin: 0;
          font-size: 1.25rem;
          font-weight: 700;
          line-height: 1.2;
          background: linear-gradient(135deg, #ffffff, #cbd5e1);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .subtitle {
          font-size: 0.75rem;
          color: #94a3b8;
          font-weight: 400;
        }
      }

      &.hidden {
        opacity: 0;
        pointer-events: none;
      }
    }

    .toggle-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #fff;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 8px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      flex-shrink: 0;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      svg {
        transition: transform 0.3s ease;
      }
    }
  }

  .nav-section {
    margin-bottom: 1.5rem;

    .section-title {
      font-size: 0.75rem;
      font-weight: 600;
      color: #94a3b8;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 0.75rem;
      padding: 0 0.75rem;

      &.hidden {
        display: none;
      }
    }
  }

  .nav-links {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin: 0.25rem 0;

      .nav-link {
        color: #cbd5e1;
        text-decoration: none;
        display: flex;
        align-items: center;
        padding: 0.875rem 0.75rem;
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid transparent;
        gap: 0.75rem;

        .icon-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border-radius: 10px;
          flex-shrink: 0;
          transition: all 0.3s ease;

          &.instagram {
            background: linear-gradient(135deg, #e1306c, #fd1d1d, #fcb045);
            color: white;
          }

          &.whatsapp {
            background: linear-gradient(135deg, #25d366, #128c7e);
            color: white;
          }
        }

        .link-content {
          display: flex;
          flex-direction: column;
          flex: 1;
          min-width: 0;

          .text {
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1.2;
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .description {
            font-size: 0.75rem;
            color: #64748b;
            line-height: 1.2;
            transition: all 0.3s ease;
          }

          &.hidden {
            opacity: 0;
            width: 0;
            overflow: hidden;
          }
        }

        .notification-badge {
          background: linear-gradient(135deg, #ef4444, #dc2626);
          color: white;
          font-size: 0.75rem;
          font-weight: 600;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          min-width: 20px;
          text-align: center;
          line-height: 1;
          box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
          animation: pulse 2s infinite;

          &.hidden {
            display: none;
          }
        }

        &:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.1);
          transform: translateX(4px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          .link-content .description {
            color: #94a3b8;
          }
        }

        &.active {
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          color: #fff;
          border-color: rgba(59, 130, 246, 0.3);
          box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);

          .link-content .description {
            color: #bfdbfe;
          }

          &::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: linear-gradient(180deg, #60a5fa, #3b82f6);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 2px 8px rgba(96, 165, 250, 0.5);
          }
        }
      }
    }

    &.hidden {
      display: none;
    }
  }

  .sidebar-footer {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .user-profile {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;

      .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        border-radius: 10px;
        color: white;
        flex-shrink: 0;
      }

      .user-info {
        display: flex;
        flex-direction: column;
        min-width: 0;

        .username {
          font-size: 0.875rem;
          font-weight: 500;
          color: #f1f5f9;
          line-height: 1.2;
        }

        .status {
          font-size: 0.75rem;
          color: #22c55e;
          line-height: 1.2;
          display: flex;
          align-items: center;
          gap: 0.25rem;

          &::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #22c55e;
            border-radius: 50%;
            animation: pulse 2s infinite;
          }
        }
      }

      &:hover {
        background: rgba(255, 255, 255, 0.08);
      }

      &.hidden {
        display: none;
      }
    }

    &.hidden {
      display: none;
    }
  }

  // Collapsed state for desktop
  &.collapsed:not(.mobile) {
    width: 80px;

    .sidebar-header .logo .logo-text {
      opacity: 0;
      pointer-events: none;
    }

    .nav-section .section-title {
      opacity: 0;
      pointer-events: none;
    }

    .nav-links .nav-link .link-content {
      opacity: 0;
      width: 0;
      overflow: hidden;
    }

    .nav-links .nav-link .notification-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      min-width: 16px;
      height: 16px;
      padding: 0;
      font-size: 0.625rem;
      line-height: 16px;
    }

    .sidebar-footer .user-profile .user-info {
      opacity: 0;
      width: 0;
      overflow: hidden;
    }
  }

  // Mobile styles
  &.mobile {
    transform: translateX(-100%);
    width: 320px;
    box-shadow: 8px 0 32px rgba(0, 0, 0, 0.3);

    .sidebar-header .toggle-btn {
      display: block;
    }

    &:not(.collapsed) {
      transform: translateX(0);
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Desktop toggle button (always visible)
@media (min-width: 769px) {
  .sidebar .sidebar-header .toggle-btn {
    display: block !important;
  }
}

// Sidebar overlay for mobile
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Mobile toggle button (when sidebar is collapsed)
@media (max-width: 768px) {
  .mobile-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background-color: #1e293b;
    border: none;
    color: #fff;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &:hover {
      background-color: #334155;
    }

    .hamburger-icon {
      display: flex;
      flex-direction: column;
      width: 20px;
      height: 15px;
      justify-content: space-between;

      span {
        display: block;
        height: 2px;
        width: 100%;
        background-color: #fff;
        transition: all 0.3s ease;
      }
    }
  }
}