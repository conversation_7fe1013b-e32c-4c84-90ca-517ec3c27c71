.instagram-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #e1306c, #fd1d1d, #fcb045);
    color: white;

    .platform-info {
      display: flex;
      align-items: center;
      gap: 1rem;

      .platform-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        backdrop-filter: blur(10px);
      }

      .platform-details {
        h2 {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 700;
        }

        p {
          margin: 0.25rem 0 0 0;
          opacity: 0.9;
          font-size: 0.875rem;
        }
      }
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: rgba(255, 255, 255, 0.2);
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.875rem;
      backdrop-filter: blur(10px);

      .status-dot {
        width: 8px;
        height: 8px;
        background: #22c55e;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
    }
  }

  .chat-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;

    .welcome-message {
      text-align: center;
      max-width: 400px;

      h3 {
        margin: 0 0 1rem 0;
        font-size: 1.5rem;
        color: #1e293b;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #64748b;
        font-size: 1rem;
        line-height: 1.6;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}