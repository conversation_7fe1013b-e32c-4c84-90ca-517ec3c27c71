<div class="whatsapp-container">
  <!-- Contact List Sidebar -->
  <div class="contacts-sidebar" [class.hidden]="selectedContact && isMobile">
    <div class="contacts-header">
      <div class="header-content">
        <div class="profile-section">
          <div class="profile-avatar">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h2>WhatsApp Business</h2>
        </div>
        <div class="header-actions">
          <button class="action-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          <button class="action-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="1" fill="currentColor"/>
              <circle cx="19" cy="12" r="1" fill="currentColor"/>
              <circle cx="5" cy="12" r="1" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="search-bar">
        <div class="search-input">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input type="text" placeholder="Search or start new chat" [(ngModel)]="searchTerm">
        </div>
      </div>
    </div>

    <div class="contacts-list">
      <div class="contact-item"
           *ngFor="let contact of filteredContacts"
           (click)="selectContact(contact)"
           [class.active]="selectedContact?.id === contact.id">
        <div class="contact-avatar">
          <img [src]="contact.avatar" [alt]="contact.name" *ngIf="contact.avatar">
          <div class="avatar-placeholder" *ngIf="!contact.avatar">
            {{ contact.name.charAt(0).toUpperCase() }}
          </div>
        </div>
        <div class="contact-info">
          <div class="contact-header">
            <h3 class="contact-name">{{ contact.name }}</h3>
            <span class="last-message-time">{{ contact.lastMessageTime }}</span>
          </div>
          <div class="contact-preview">
            <p class="last-message">{{ contact.lastMessage }}</p>
            <div class="message-status">
              <span class="unread-count" *ngIf="contact.unreadCount > 0">{{ contact.unreadCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Chat Area -->
  <div class="chat-area" [class.full-width]="!selectedContact">
    <!-- Chat Header -->
    <div class="chat-header" *ngIf="selectedContact">
      <button class="back-btn" (click)="goBackToContacts()" *ngIf="isMobile">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5" stroke="currentColor" stroke-width="2"/>
          <path d="M12 19L5 12L12 5" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>
      <div class="contact-info">
        <div class="avatar">
          <img [src]="selectedContact.avatar" [alt]="selectedContact.name" *ngIf="selectedContact.avatar">
          <div class="avatar-placeholder" *ngIf="!selectedContact.avatar">
            {{ selectedContact.name.charAt(0).toUpperCase() }}
          </div>
        </div>
        <div class="contact-details">
          <h3>{{ selectedContact.name }}</h3>
          <span class="status">{{ selectedContact.status }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button class="action-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 16.92V19a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h2.09a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9a16 16 0 0 0 6 6l.36-1.36a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92Z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>
        <button class="action-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="1" fill="currentColor"/>
            <circle cx="19" cy="12" r="1" fill="currentColor"/>
            <circle cx="5" cy="12" r="1" fill="currentColor"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Chat Messages -->
    <div class="chat-body" *ngIf="selectedContact">
      <div class="date-separator">
        <span>Today</span>
      </div>

      <div class="system-message">
        <p>This business uses a secure service from Meta to manage this chat. Click to learn more.</p>
      </div>

      <div *ngFor="let msg of getContactMessages(selectedContact.id)"
           [ngClass]="'message-wrapper ' + (msg.type === 'sent' ? 'sent' : 'received')">
        <div class="message">
          <div class="message-content">{{ msg.text }}</div>
          <div class="message-time">
            <span>{{ msg.time }}</span>
            <span *ngIf="msg.type === 'sent'" class="message-status">✓✓</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Input -->
    <div class="chat-input" *ngIf="selectedContact">
      <button class="attach-btn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21.44 11.05L12.25 20.24a6 6 0 1 1-8.49-8.49L12.95 2.56a4 4 0 1 1 5.66 5.66L9.42 17.41a2 2 0 1 1-2.83-2.83L15.07 6.1" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>
      <div class="input-container">
        <input
          [(ngModel)]="message"
          type="text"
          placeholder="Type a message"
          (keyup.enter)="sendMessage()" />
        <button class="emoji-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="2"/>
            <line x1="9" y1="9" x2="9.01" y2="9" stroke="currentColor" stroke-width="2"/>
            <line x1="15" y1="9" x2="15.01" y2="9" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>
      </div>
      <button class="send-btn" (click)="sendMessage()" [disabled]="!message.trim()">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <line x1="22" y1="2" x2="11" y2="13" stroke="currentColor" stroke-width="2"/>
          <polygon points="22,2 15,22 11,13 2,9 22,2" fill="currentColor"/>
        </svg>
      </button>
      <button class="voice-btn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3Z" stroke="currentColor" stroke-width="2"/>
          <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" stroke-width="2"/>
          <line x1="12" y1="19" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
          <line x1="8" y1="23" x2="16" y2="23" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>
    </div>

    <!-- Welcome Screen -->
    <div class="welcome-screen" *ngIf="!selectedContact">
      <div class="welcome-content">
        <div class="welcome-icon">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 11.5A8.38 8.38 0 0 1 12.62 20A8.38 8.38 0 0 1 3 11.5A8.38 8.38 0 0 1 12.62 3A8.38 8.38 0 0 1 21 11.5Z" stroke="currentColor" stroke-width="2"/>
            <path d="M8.21 13.89L7 23L16.11 15.79C17.18 14.72 18 13.26 18 11.5A6.38 6.38 0 0 0 12.62 5A6.38 6.38 0 0 0 7 11.5C7 12.81 7.35 14.02 8.21 13.89Z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h2>WhatsApp Business</h2>
        <p>Send and receive messages without keeping your phone online.<br>Use WhatsApp on up to 4 linked devices and 1 phone at the same time.</p>
      </div>
    </div>
  </div>
</div>
