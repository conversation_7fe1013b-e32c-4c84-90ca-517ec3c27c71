.whatsapp-container {
  height: 100vh;
  display: flex;
  background: #f0f2f5;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  // Contact List Sidebar
  .contacts-sidebar {
    width: 400px;
    background: #ffffff;
    border-right: 1px solid #e9edef;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;

    &.hidden {
      display: none;
    }

    .contacts-header {
      background: #00a884;
      color: white;
      padding: 1rem;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .profile-section {
          display: flex;
          align-items: center;
          gap: 0.75rem;

          .profile-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          h2 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 500;
          }
        }

        .header-actions {
          display: flex;
          gap: 0.5rem;

          .action-btn {
            background: none;
            border: none;
            color: white;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.2s;

            &:hover {
              background: rgba(255, 255, 255, 0.1);
            }
          }
        }
      }

      .search-bar {
        .search-input {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 20px;
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          gap: 0.5rem;

          svg {
            color: rgba(255, 255, 255, 0.7);
          }

          input {
            flex: 1;
            background: none;
            border: none;
            outline: none;
            color: white;
            font-size: 0.9rem;

            &::placeholder {
              color: rgba(255, 255, 255, 0.7);
            }
          }
        }
      }
    }

    .contacts-list {
      flex: 1;
      overflow-y: auto;

      .contact-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f0f2f5;
        transition: background-color 0.2s;

        &:hover {
          background: #f5f6f6;
        }

        &.active {
          background: #e7f3ff;
        }

        .contact-avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          overflow: hidden;
          background: #ddd;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .avatar-placeholder {
            background: linear-gradient(135deg, #00a884, #017561);
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .contact-info {
          flex: 1;
          min-width: 0;

          .contact-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;

            .contact-name {
              margin: 0;
              font-size: 1rem;
              font-weight: 500;
              color: #111b21;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .last-message-time {
              font-size: 0.75rem;
              color: #667781;
              white-space: nowrap;
            }
          }

          .contact-preview {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .last-message {
              margin: 0;
              font-size: 0.85rem;
              color: #667781;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              flex: 1;
            }

            .message-status {
              .unread-count {
                background: #00a884;
                color: white;
                font-size: 0.75rem;
                font-weight: 600;
                padding: 0.2rem 0.5rem;
                border-radius: 12px;
                min-width: 20px;
                text-align: center;
                line-height: 1;
              }
            }
          }
        }
      }
    }
  }

  // Chat Area
  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f0f2f5;

    &.full-width {
      width: 100%;
    }

    &.hidden {
      display: none;
    }
  }
}

  .chat-header {
    background: #f0f2f5;
    border-bottom: 1px solid #e9edef;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;

    .back-btn {
      background: none;
      border: none;
      color: #54656f;
      padding: 0.5rem;
      border-radius: 50%;
      cursor: pointer;
      display: none;

      &:hover {
        background: #f5f6f6;
      }
    }

    .contact-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        background: #ddd;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .avatar-placeholder {
          background: linear-gradient(135deg, #00a884, #017561);
          color: white;
          font-weight: 600;
          font-size: 1rem;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .contact-details {
        h3 {
          margin: 0;
          font-size: 1.1rem;
          font-weight: 500;
          color: #111b21;
        }

        .status {
          font-size: 0.85rem;
          color: #667781;
          margin: 0;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;

      .action-btn {
        background: none;
        border: none;
        color: #54656f;
        padding: 0.5rem;
        border-radius: 50%;
        cursor: pointer;

        &:hover {
          background: #f5f6f6;
        }
      }
    }
  }

  // Chat Body
  .chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzQwIiBoZWlnaHQ9IjM0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGRlZnM+CjxwYXR0ZXJuIGlkPSJhIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIiB3aWR0aD0iMzQwIiBoZWlnaHQ9IjM0MCI+CjxwYXRoIGZpbGw9IiNmMGYyZjUiIGQ9Ik0wIDBoMzQwdjM0MEgweiIvPgo8L3BhdHRlcm4+CjwvZGVmcz4KPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNhKSIvPgo8L3N2Zz4=');

  .date-separator {
    text-align: center;
    margin: 12px 0;

    span {
      background-color: #e9edef;
      color: #54656f;
      padding: 5px 12px;
      border-radius: 7.5px;
      font-size: 12.5px;
      font-weight: 500;
    }
  }

  .system-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 7.5px;
    padding: 12px;
    margin: 8px 0;
    text-align: center;

    p {
      margin: 0;
      font-size: 13px;
      color: #54656f;
    }
  }

  .message-wrapper {
    display: flex;
    margin: 4px 0;

    &.sent {
      justify-content: flex-end;

      .message {
        background-color: #d9fdd3;
        margin-left: 64px;
      }
    }

    &.received {
      justify-content: flex-start;

      .message {
        background-color: #ffffff;
        margin-right: 64px;
      }
    }

    .message {
      max-width: 65%;
      border-radius: 7.5px;
      padding: 6px 7px 8px 9px;
      box-shadow: 0 1px 0.5px rgba(11, 20, 26, 0.13);
      position: relative;

      .message-content {
        font-size: 14.2px;
        line-height: 19px;
        color: #111b21;
        margin-bottom: 4px;
      }

      .message-time {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;
        font-size: 11px;
        color: #667781;

        .message-status {
          color: #53bdeb;
          font-size: 16px;
          line-height: 1;
        }
      }
    }
  }

  // Chat Input
  .chat-input {
  display: flex;
  align-items: center;
  padding: 5px 16px;
  background-color: #f0f2f5;
  gap: 8px;

  .attach-btn,
  .send-btn,
  .voice-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    color: #54656f;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;

    &:hover {
      background-color: #f5f6f6;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .send-btn {
    color: #00a884;

    &:disabled {
      color: #54656f;
    }
  }

  .input-container {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 21px;
    padding: 9px 12px;
    gap: 8px;

    input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 15px;
      color: #111b21;
      background: transparent;

      &::placeholder {
        color: #667781;
      }
    }

    .emoji-btn {
      background: none;
      border: none;
      padding: 4px;
      cursor: pointer;
      color: #54656f;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #111b21;
      }
    }
  }
}

  // Welcome Screen
  .welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f2f5;

    .welcome-content {
      text-align: center;
      max-width: 400px;
      padding: 2rem;

      .welcome-icon {
        margin-bottom: 2rem;
        color: #00a884;
      }

      h2 {
        margin: 0 0 1rem 0;
        font-size: 2rem;
        color: #111b21;
        font-weight: 300;
      }

      p {
        margin: 0;
        color: #667781;
        font-size: 0.9rem;
        line-height: 1.6;
      }
    }
  }
}

// Mobile responsive styles
@media (max-width: 768px) {
  .whatsapp-container {
    .contacts-sidebar {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1000;
      transform: translateX(0);

      &.hidden {
        transform: translateX(-100%);
      }
    }

    .chat-area {
      width: 100%;

      .chat-header .back-btn {
        display: block;
      }

      &.hidden {
        display: none;
      }
    }
  }
}

// Desktop styles - ensure sidebar is always visible
@media (min-width: 769px) {
  .whatsapp-container {
    .contacts-sidebar {
      display: flex !important;
      transform: translateX(0) !important;
    }

    .chat-area {
      display: flex !important;
    }
  }
}