import { NgClass, CommonModule } from '@angular/common';
import { Component, HostListener, Inject, PLATFORM_ID, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { isPlatformBrowser } from '@angular/common';

interface Contact {
  id: string;
  name: string;
  avatar?: string;
  status: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
}

interface Message {
  id: string;
  contactId: string;
  text: string;
  type: 'sent' | 'received';
  time: string;
  timestamp: Date;
}

@Component({
  selector: 'app-chat-window',
  standalone: true,
  imports: [NgClass, CommonModule, FormsModule],
  templateUrl: './chat-window.component.html',
  styleUrl: './chat-window.component.scss'
})
export class ChatWindowComponent implements OnInit {
  message = '';
  searchTerm = '';
  selectedContact: Contact | null = null;
  isMobile = false;
  private isBrowser: boolean;

  contacts: Contact[] = [
    {
      id: '1',
      name: 'Smart AI Agent',
      avatar: 'assets/images/smart-ai-agent.png',
      status: 'Business Account',
      lastMessage: 'Hi! Thanks for messaging us. This is an automated reply.',
      lastMessageTime: '10:46 PM',
      unreadCount: 2
    },
    {
      id: '2',
      name: 'John Doe',
      status: 'Online',
      lastMessage: 'Hey, how are you?',
      lastMessageTime: '9:30 PM',
      unreadCount: 1
    },
    {
      id: '3',
      name: 'Sarah Wilson',
      status: 'Last seen 2 hours ago',
      lastMessage: 'Thanks for the help!',
      lastMessageTime: '8:15 PM',
      unreadCount: 0
    },
    {
      id: '4',
      name: 'Mike Johnson',
      status: 'Online',
      lastMessage: 'Can we schedule a meeting?',
      lastMessageTime: 'Yesterday',
      unreadCount: 3
    },
    {
      id: '5',
      name: 'Emma Davis',
      status: 'Last seen 1 hour ago',
      lastMessage: 'Perfect! See you tomorrow.',
      lastMessageTime: 'Yesterday',
      unreadCount: 0
    }
  ];

  allMessages: Message[] = [
    {
      id: '1',
      contactId: '1',
      text: 'Hello',
      type: 'sent',
      time: '10:45 PM',
      timestamp: new Date()
    },
    {
      id: '2',
      contactId: '1',
      text: 'Hi! Thanks for messaging us. This is an automated reply.',
      type: 'received',
      time: '10:46 PM',
      timestamp: new Date()
    },
    {
      id: '3',
      contactId: '2',
      text: 'Hey, how are you?',
      type: 'received',
      time: '9:30 PM',
      timestamp: new Date()
    },
    {
      id: '4',
      contactId: '3',
      text: 'Thanks for the help!',
      type: 'received',
      time: '8:15 PM',
      timestamp: new Date()
    },
    {
      id: '5',
      contactId: '4',
      text: 'Can we schedule a meeting?',
      type: 'received',
      time: '7:20 PM',
      timestamp: new Date()
    },
    {
      id: '6',
      contactId: '5',
      text: 'Perfect! See you tomorrow.',
      type: 'received',
      time: '6:15 PM',
      timestamp: new Date()
    }
  ];

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.checkScreenSize();
  }

  ngOnInit() {
    // Auto-select first contact for better UX on desktop
    if (this.contacts.length > 0 && !this.isMobile) {
      this.selectedContact = this.contacts[0];
    }
  }

  @HostListener('window:resize')
  onResize() {
    if (this.isBrowser) {
      this.checkScreenSize();
    }
  }

  private checkScreenSize() {
    if (this.isBrowser && typeof window !== 'undefined') {
      this.isMobile = window.innerWidth <= 768;
    }
  }

  get filteredContacts(): Contact[] {
    if (!this.searchTerm) {
      return this.contacts;
    }
    return this.contacts.filter(contact =>
      contact.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  selectContact(contact: Contact) {
    this.selectedContact = contact;
    // Mark messages as read
    contact.unreadCount = 0;
  }

  goBackToContacts() {
    this.selectedContact = null;
  }

  getContactMessages(contactId: string): Message[] {
    return this.allMessages.filter(msg => msg.contactId === contactId);
  }

  sendMessage() {
    if (this.message.trim() && this.selectedContact) {
      const newMessage: Message = {
        id: Date.now().toString(),
        contactId: this.selectedContact.id,
        text: this.message,
        type: 'sent',
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        timestamp: new Date()
      };

      this.allMessages.push(newMessage);

      // Update contact's last message
      this.selectedContact.lastMessage = this.message;
      this.selectedContact.lastMessageTime = newMessage.time;

      this.message = '';

      // Simulate a response
      setTimeout(() => {
        if (this.selectedContact) {
          const responseMessage: Message = {
            id: (Date.now() + 1).toString(),
            contactId: this.selectedContact.id,
            text: 'Thanks for your message! We\'ll get back to you soon.',
            type: 'received',
            time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            timestamp: new Date()
          };

          this.allMessages.push(responseMessage);
          this.selectedContact.lastMessage = responseMessage.text;
          this.selectedContact.lastMessageTime = responseMessage.time;
        }
      }, 1000);
    }
  }
}
